# Group Join Functionality Test Plan

## Implementation Summary

The POST /groups/:id/join endpoint has been implemented with the following features:

### Backend Implementation:
1. **Existing functionality** (already working):
   - `POST /groups/:id/join` - Request to join a group
   - `POST /groups/:id/join?action=accept` - Accept join request (only group creators)

2. **New functionality added**:
   - `GET /groups/:id/join-requests` - Get pending join requests for a group (only group creators)
   - `POST /groups/:id/join?action=reject` - Reject join request (only group creators)

### Frontend Implementation:
1. **Enhanced Groups Page** (`/groups`):
   - Shows all groups with join buttons
   - Displays group privacy settings
   - Shows "Your Group" for groups created by current user
   - Shows join status (pending, member, etc.)
   - Added "Manage Requests" button for group creators

2. **New Join Requests Management Page** (`/groups/join-requests`):
   - Lists all groups created by current user
   - Shows pending join requests for each group
   - Accept/Reject buttons for each request
   - Real-time updates when processing requests

### Database Schema:
- Uses existing `group_members` table with `status` field ('pending', 'active')
- No new tables needed

## Test Cases to Verify:

### 1. Join Group Request
```bash
# User requests to join a group
curl -X POST http://localhost:8080/api/groups/1/join \
  -H "Content-Type: application/json" \
  -b "social-network=<session_cookie>"
```

### 2. Get Pending Requests (Group Creator Only)
```bash
# Group creator views pending requests
curl -X GET http://localhost:8080/api/groups/1/join-requests \
  -H "Content-Type: application/json" \
  -b "social-network=<session_cookie>"
```

### 3. Accept Join Request (Group Creator Only)
```bash
# Group creator accepts a join request
curl -X POST "http://localhost:8080/api/groups/1/join?action=accept" \
  -H "Content-Type: application/json" \
  -d '{"user_id": "2"}' \
  -b "social-network=<session_cookie>"
```

### 4. Reject Join Request (Group Creator Only)
```bash
# Group creator rejects a join request
curl -X POST "http://localhost:8080/api/groups/1/join?action=reject" \
  -H "Content-Type: application/json" \
  -d '{"user_id": "2"}' \
  -b "social-network=<session_cookie>"
```

## Frontend Test Cases:

### 1. Groups Page (`http://localhost:3000/groups`)
- [ ] Shows all groups
- [ ] Join button appears for groups user is not a member of
- [ ] "Your Group" label appears for groups created by current user
- [ ] "Manage Requests" button appears for group creators
- [ ] Join request shows "Request Pending" after clicking join

### 2. Join Requests Page (`http://localhost:3000/groups/join-requests`)
- [ ] Shows only groups created by current user
- [ ] Lists pending join requests for each group
- [ ] Accept button works and removes request from list
- [ ] Reject button works and removes request from list
- [ ] Shows "No pending requests" when no requests exist

## Security Verification:
- [ ] Only group creators can view join requests
- [ ] Only group creators can accept/reject requests
- [ ] Users cannot request to join their own groups
- [ ] Users cannot request to join groups they're already members of
- [ ] Proper authentication required for all endpoints

## Files Modified/Created:

### Backend:
- `backend/internal/model/group-join-request.go` (NEW)
- `backend/internal/repository/group_repository.go` (MODIFIED)
- `backend/internal/service/group_service.go` (MODIFIED)
- `backend/internal/handler/group_handlers.go` (MODIFIED)
- `backend/internal/routes/routes.go` (MODIFIED)

### Frontend:
- `frontend/src/app/groups/page.js` (MODIFIED)
- `frontend/src/app/groups/join-requests/page.js` (NEW)

## Expected Behavior:
1. Users can request to join groups they're not members of
2. Group creators can view all pending requests for their groups
3. Group creators can accept or reject join requests
4. Only group creators have access to manage join requests
5. UI updates in real-time when requests are processed
6. Proper error handling and user feedback
